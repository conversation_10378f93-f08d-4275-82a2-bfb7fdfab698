<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Promz - AI Prompt Management Tool</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="images/favicon.png">
</head>

<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically
        fetch('includes/header.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('about.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            });
    </script>

    <section class="page-header">
        <div class="container">
            <h1>About Promz</h1>
        </div>
    </section>

    <section class="about-content">
        <div class="container">
            <div class="about-section">
                <h2>Our Mission</h2>
                <p>At Promz, we're on a mission to simplify AI prompt management and collaboration. We believe that the
                    power of AI should be accessible to everyone, and well-crafted prompts are the key to unlocking that
                    potential.</p>
                <p>Our platform provides a comprehensive solution for organizing, optimizing, and collaborating on AI
                    prompts, making it easier for individuals and teams to harness the power of AI in their workflows.
                </p>
            </div>

            <div class="about-section">
                <h2>Our Story</h2>
                <p>Promz was founded in 2024 by a team of AI enthusiasts who recognized the growing importance of prompt
                    engineering in the AI ecosystem. As AI language models became more powerful, the need for a
                    dedicated tool to manage and optimize prompts became increasingly apparent.</p>
                <p>What started as a simple tool for personal use has evolved into a comprehensive platform that serves
                    individuals, teams, and organizations across various industries.</p>
            </div>

            <div class="about-section">
                <h2>Our Technology</h2>
                <p>Promz is built on a modern technology stack that ensures reliability, performance, and security:</p>
                <ul>
                    <li><strong>Client Application:</strong> Developed with Flutter for cross-platform compatibility
                    </li>
                    <li><strong>Backend API:</strong> Powered by Go for high performance and reliability</li>
                    <li><strong>Database:</strong> Advanced data storage with real-time synchronization</li>
                    <li><strong>AI Integration:</strong> Seamless connection with popular AI providers</li>
                </ul>
                <p>Our architecture follows the MVVM pattern on the client side, ensuring a clean separation of concerns
                    and maintainable codebase.</p>
            </div>

            <div class="about-section">
                <h2>Our Team</h2>
                <p>Promz is developed by a diverse team of engineers, designers, and AI specialists who are passionate
                    about creating tools that empower users to get the most out of AI technology.</p>
                <p>We're committed to continuous improvement and innovation, regularly incorporating user feedback to
                    enhance the platform and add new features.</p>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically
        fetch('includes/footer.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            });
    </script>

    <script src="js/main.js"></script>
</body>

</html>