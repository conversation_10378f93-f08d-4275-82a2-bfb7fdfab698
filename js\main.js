/**
 * Promz Static Website JavaScript
 * Provides interactivity for the Promz marketing website
 */

document.addEventListener('DOMContentLoaded', function () {
    // Mobile navigation toggle functionality
    const setupMobileNav = () => {
        const navToggle = document.createElement('button');
        navToggle.classList.add('mobile-nav-toggle');
        navToggle.setAttribute('aria-label', 'Toggle navigation menu');
        navToggle.innerHTML = '<span></span><span></span><span></span>';

        const header = document.querySelector('header .container');
        const nav = document.querySelector('nav');

        if (window.innerWidth <= 768) {
            if (!document.querySelector('.mobile-nav-toggle')) {
                header.insertBefore(navToggle, nav);
                nav.classList.add('mobile-nav');
            }
        }

        navToggle.addEventListener('click', function () {
            nav.classList.toggle('open');
            this.classList.toggle('open');
        });
    };

    // Initialize mobile navigation
    setupMobileNav();
    window.addEventListener('resize', setupMobileNav);

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80, // Account for fixed header
                    behavior: 'smooth'
                });

                // Close mobile nav if open
                const mobileNav = document.querySelector('nav.mobile-nav');
                const navToggle = document.querySelector('.mobile-nav-toggle');
                if (mobileNav && mobileNav.classList.contains('open')) {
                    mobileNav.classList.remove('open');
                    navToggle.classList.remove('open');
                }
            }
        });
    });

    // Add animation class to elements when they're in viewport
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.feature-card, .download-button');

        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementTop < windowHeight - 100) {
                element.classList.add('animate');
            }
        });
    };

    // Initialize animations
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Run once on page load

    // Current year for copyright
    const yearElement = document.querySelector('.copyright-year');
    if (yearElement) {
        yearElement.textContent = new Date().getFullYear();
    }
});