#!/bin/bash
# <PERSON>ript to update the versions registry JSON file
# Usage: ./update_versions_registry.sh <version> <build_number> [is_beta]
# Example: ./update_versions_registry.sh "1.0.25" "26" true

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check parameters
if [ "$#" -lt 2 ]; then
  echo -e "${YELLOW}Usage: $0 <version> <build_number> [is_beta]${NC}"
  echo -e "${YELLOW}Example: $0 \"1.0.25\" \"26\" true${NC}"
  exit 1
fi

VERSION="$1"
BUILD_NUMBER="$2"
IS_BETA="${3:-false}"

# Get the web directory
WEB_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REGISTRY_FILE="$WEB_DIR/releases/versions.json"

echo -e "${BLUE}Updating versions registry for version $VERSION (build $BUILD_NUMBER)${NC}"

# Create registry file if it doesn't exist
if [ ! -f "$REGISTRY_FILE" ]; then
  echo -e "${YELLOW}Creating new versions registry file${NC}"
  echo '{
  "lastUpdated": "'$(date -Iseconds)'",
  "versions": []
}' > "$REGISTRY_FILE"
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
  echo -e "${YELLOW}Warning: jq is not installed. Using temporary file method.${NC}"
  
  # Read the current registry
  REGISTRY_CONTENT=$(cat "$REGISTRY_FILE")
  
  # Parse the JSON manually and check if version exists
  if echo "$REGISTRY_CONTENT" | grep -q "\"version\": \"$VERSION\""; then
    # Version exists, update it
    echo -e "${BLUE}Updating existing version $VERSION${NC}"
    # This is a simplified approach without jq - in production you'd want to use jq
    # For now, we'll just recreate the file with the updated version
    
    # Extract versions array without the target version
    VERSIONS_WITHOUT_TARGET=$(echo "$REGISTRY_CONTENT" | grep -v "\"version\": \"$VERSION\"" | grep -v "\"buildNumber\": \".*\"," | grep -v "\"releaseDate\": \".*\"," | grep -v "\"inProduction\": .*")
    
    # Create new entry
    NEW_ENTRY='{
      "version": "'$VERSION'",
      "buildNumber": "'$BUILD_NUMBER'",
      "releaseDate": "'$(date +%Y-%m-%d)'",
      "inProduction": '$([ "$IS_BETA" = "true" ] && echo "false" || echo "true")'
    }'
    
    # Create new file with updated content
    echo '{
  "lastUpdated": "'$(date -Iseconds)'",
  "versions": [
    '$NEW_ENTRY'
    '"$VERSIONS_WITHOUT_TARGET"'
  ]
}' > "$REGISTRY_FILE"
  else
    # Version doesn't exist, add it
    echo -e "${BLUE}Adding new version $VERSION${NC}"
    # Again, this is a simplified approach
    # Extract the versions array
    VERSIONS=$(echo "$REGISTRY_CONTENT" | sed -n '/versions/,/]/p' | sed '1d;$d')
    
    # Create new entry
    NEW_ENTRY='{
      "version": "'$VERSION'",
      "buildNumber": "'$BUILD_NUMBER'",
      "releaseDate": "'$(date +%Y-%m-%d)'",
      "inProduction": '$([ "$IS_BETA" = "true" ] && echo "false" || echo "true")'
    }'
    
    # Create new file with updated content
    echo '{
  "lastUpdated": "'$(date -Iseconds)'",
  "versions": [
    '$NEW_ENTRY','
    '"$VERSIONS"'
  ]
}' > "$REGISTRY_FILE"
  fi
else
  # Use jq for proper JSON manipulation
  echo -e "${BLUE}Using jq for JSON manipulation${NC}"
  
  # Check if this version already exists in the registry
  if jq -e '.versions[] | select(.version == "'"$VERSION"'")' "$REGISTRY_FILE" > /dev/null; then
    # Update existing version
    jq --arg version "$VERSION" \
       --arg build "$BUILD_NUMBER" \
       --argjson prod $([ "$IS_BETA" = "true" ] && echo "false" || echo "true") \
       '.versions = (.versions | map(if .version == $version then .buildNumber = $build | .inProduction = $prod else . end))' \
       "$REGISTRY_FILE" > "${REGISTRY_FILE}.tmp" && mv "${REGISTRY_FILE}.tmp" "$REGISTRY_FILE"
  else
    # Add new version
    jq --arg version "$VERSION" \
       --arg build "$BUILD_NUMBER" \
       --arg date "$(date +%Y-%m-%d)" \
       --argjson prod $([ "$IS_BETA" = "true" ] && echo "false" || echo "true") \
       '.versions = [{version: $version, buildNumber: $build, releaseDate: $date, inProduction: $prod}] + .versions' \
       "$REGISTRY_FILE" > "${REGISTRY_FILE}.tmp" && mv "${REGISTRY_FILE}.tmp" "$REGISTRY_FILE"
  fi
  
  # Update lastUpdated timestamp
  jq --arg date "$(date -Iseconds)" '.lastUpdated = $date' "$REGISTRY_FILE" > "${REGISTRY_FILE}.tmp" && mv "${REGISTRY_FILE}.tmp" "$REGISTRY_FILE"
fi

echo -e "${GREEN}Successfully updated versions registry for version $VERSION (build $BUILD_NUMBER)${NC}"
echo -e "${GREEN}Registry file: $REGISTRY_FILE${NC}"

# Pretty print the updated registry
if command -v jq &> /dev/null; then
  echo -e "${BLUE}Updated registry content:${NC}"
  jq '.' "$REGISTRY_FILE"
else
  echo -e "${BLUE}Updated registry content (raw):${NC}"
  cat "$REGISTRY_FILE"
fi
