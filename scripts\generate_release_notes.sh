#!/bin/bash
# Release Notes Generation Script for Promz
# Automates the generation of release notes from git history
# Follows project conventions from existing scripts

# Set script directory and workspace root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
WEB_DIR="$WORKSPACE_ROOT/web"
CLIENT_DIR="$WORKSPACE_ROOT/client"

# File paths
BETA_RELEASE_FILE="$WEB_DIR/beta/releases/index.html"
PROD_RELEASE_FILE="$WEB_DIR/releases/index.html"
PUBSPEC_FILE="$CLIENT_DIR/pubspec.yaml"
VERSIONS_REGISTRY="$WEB_DIR/releases/versions.json"
UPDATE_REGISTRY_SCRIPT="$WEB_DIR/scripts/update_versions_registry.sh"

# Color definitions (following project convention)
set_colors() {
    GREEN='\033[0;32m'
    YELLOW='\033[0;33m'
    RED='\033[0;31m'
    BLUE='\033[0;34m'
    CYAN='\033[0;36m'
    NC='\033[0m'
}

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1" >&2
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "Not in a git repository"
        return 1
    fi

    # Check required tools
    local tools=("git" "date" "grep" "sed" "awk")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" > /dev/null 2>&1; then
            log_error "Required tool '$tool' not found"
            return 1
        fi
    done

    # Check optional tools for enhanced functionality
    local optional_tools=("grpcurl" "jq" "curl")
    for tool in "${optional_tools[@]}"; do
        if ! command -v "$tool" > /dev/null 2>&1; then
            log_warn "Optional tool '$tool' not found - some features may be limited"
            if [[ "$tool" == "grpcurl" ]]; then
                log_warn "  Install with: brew install grpcurl (for AI content optimization)"
            elif [[ "$tool" == "jq" ]]; then
                log_warn "  Install with: brew install jq (for better JSON parsing)"
            elif [[ "$tool" == "curl" ]]; then
                log_warn "  curl is required for server health checks"
            fi
        fi
    done

    # Check if pubspec.yaml exists
    if [[ ! -f "$PUBSPEC_FILE" ]]; then
        log_error "pubspec.yaml not found at $PUBSPEC_FILE"
        return 1
    fi

    # Check if target directories exist
    if [[ ! -d "$WEB_DIR/beta/releases" ]]; then
        log_warn "Beta releases directory doesn't exist, creating..."
        mkdir -p "$WEB_DIR/beta/releases"
    fi

    if [[ ! -d "$WEB_DIR/releases" ]]; then
        log_warn "Production releases directory doesn't exist, creating..."
        mkdir -p "$WEB_DIR/releases"
    fi

    log_info "Prerequisites check completed successfully"
    return 0
}

# Extract current version from pubspec.yaml
extract_current_version() {
    local version_line=$(grep "^version:" "$PUBSPEC_FILE" | head -1)
    if [[ -z "$version_line" ]]; then
        log_error "Could not find version in pubspec.yaml"
        return 1
    fi

    # Extract version (e.g., "1.0.24+25" -> "1.0.24")
    local version=$(echo "$version_line" | sed 's/^version:\s*//' | sed 's/+.*//')
    echo "$version"
}

# Extract last release metadata from HTML file
extract_last_release_info() {
    local html_file="$1"
    local last_date=""
    local last_version=""

    if [[ -f "$html_file" ]]; then
        # Look for hidden metadata tags using awk for more reliable parsing
        last_date=$(awk '/<!-- LAST_UPDATED:/ {gsub(/<!-- LAST_UPDATED: /, ""); gsub(/ -->/, ""); print; exit}' "$html_file" 2>/dev/null)
        last_version=$(awk '/<!-- LAST_VERSION:/ {gsub(/<!-- LAST_VERSION: /, ""); gsub(/ -->/, ""); print; exit}' "$html_file" 2>/dev/null)

        # Clean up any remaining whitespace
        last_date=$(echo "$last_date" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        last_version=$(echo "$last_version" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

        # If no metadata tags found, try to extract from visible content
        if [[ -z "$last_date" ]]; then
            # Look for "Last updated:" text in the HTML
            last_date=$(awk '/Last updated:/ {gsub(/.*Last updated: /, ""); gsub(/<.*/, ""); print; exit}' "$html_file" 2>/dev/null)
            last_date=$(echo "$last_date" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        fi

        # If still no date, try to extract version from h2 tags
        if [[ -z "$last_version" ]]; then
            last_version=$(awk '/<h2>Version/ {gsub(/.*<h2>Version /, ""); gsub(/<.*/, ""); print; exit}' "$html_file" 2>/dev/null)
            last_version=$(echo "$last_version" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        fi

        # Validate date format (macOS compatible)
        if [[ -n "$last_date" ]]; then
            # Test if date can be parsed - use different approach for macOS vs GNU date
            local test_date=""
            if date -d "$last_date" "+%Y-%m-%d %H:%M:%S" >/dev/null 2>&1; then
                # GNU date (Linux)
                test_date=$(date -d "$last_date" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
            elif date -j -f "%Y-%m-%d %H:%M:%S" "$last_date" "+%Y-%m-%d %H:%M:%S" >/dev/null 2>&1; then
                # BSD date (macOS)
                test_date=$(date -j -f "%Y-%m-%d %H:%M:%S" "$last_date" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
            fi

            if [[ -z "$test_date" ]]; then
                log_debug "Invalid date format detected: '$last_date', clearing..."
                last_date=""
            fi
        fi
    fi

    echo "$last_date|$last_version"
}

# Get git commits since last release with improved fallback logic
get_commits_since_last_release() {
    local last_date="$1"
    local last_version="$2"
    local git_args=""
    local use_date_filter=false

    # Try date-based filtering first if we have a valid date
    if [[ -n "$last_date" ]]; then
        # Validate and convert date to git format (macOS compatible)
        local git_date=""
        if date -d "$last_date" "+%Y-%m-%d %H:%M:%S" >/dev/null 2>&1; then
            # GNU date (Linux)
            git_date=$(date -d "$last_date" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
        elif date -j -f "%Y-%m-%d %H:%M:%S" "$last_date" "+%Y-%m-%d %H:%M:%S" >/dev/null 2>&1; then
            # BSD date (macOS) - input is already in correct format
            git_date="$last_date"
        fi

        if [[ -n "$git_date" ]]; then
            git_args="--since=\"$git_date\""
            use_date_filter=true
            log_debug "Using date-based filtering since: $git_date"
        else
            log_debug "Date parsing failed for '$last_date'"
        fi
    fi

    # Fallback to version-based filtering if date filtering failed
    if [[ "$use_date_filter" == false ]]; then
        if [[ -n "$last_version" ]]; then
            # Try to find commits since the last version tag
            local version_tag="v$last_version"
            # Escape special characters in version tag for grep
            local escaped_tag=$(printf '%s\n' "$version_tag" | sed 's/[[\.*^$()+?{|]/\\&/g')
            if git tag -l | grep -q "^${escaped_tag}$"; then
                git_args="$version_tag..HEAD"
                log_debug "Using version-based filtering since tag: $version_tag"
            else
                # Try alternative tag formats
                version_tag="$last_version"
                escaped_tag=$(printf '%s\n' "$version_tag" | sed 's/[[\.*^$()+?{|]/\\&/g')
                if git tag -l | grep -q "^${escaped_tag}$"; then
                    git_args="$version_tag..HEAD"
                    log_debug "Using version-based filtering since tag: $version_tag"
                else
                    log_warn "No valid date or version tag found, using last 30 days" >&2
                    git_args="--since=\"30 days ago\""
                fi
            fi
        else
            log_warn "No last release date or version found, using last 30 days" >&2
            git_args="--since=\"30 days ago\""
        fi
    fi

    # Get commits with one-line format, redirect stderr to avoid mixing with output
    if [[ "$git_args" =~ \.\. ]]; then
        # Version-based range query
        eval "git log $git_args --oneline --no-merges 2>/dev/null"
    else
        # Date-based query
        eval "git log $git_args --oneline --no-merges 2>/dev/null"
    fi
}

# Filter commits to exclude non-user-facing changes
filter_user_facing_commits() {
    local commits="$1"

    # More comprehensive exclude patterns
    local exclude_patterns=(
        "^[a-f0-9]+ chore:"
        "^[a-f0-9]+ docs:"
        "^[a-f0-9]+ test:"
        "^[a-f0-9]+ ci:"
        "^[a-f0-9]+ build:"
        "^[a-f0-9]+ refactor:"
        "^[a-f0-9]+ style:"
        "internal"
        "cleanup"
        "formatting"
        "lint"
        "Merge"
        "merge"
        "whitespace"
        "update subproject commit"
        "regenerated"
        "version control"
        "gitignore"
        "remove.*debug"
        "logging"
        "comment"
        "rename.*for.*consistency"
        "update.*import"
        "analysis_options"
    )

    local filtered_commits="$commits"

    for pattern in "${exclude_patterns[@]}"; do
        filtered_commits=$(echo "$filtered_commits" | grep -v -i "$pattern")
    done

    # Additional filtering for very technical commits
    filtered_commits=$(echo "$filtered_commits" | grep -v -E "(proto|grpc|websocket).*refactor" | grep -v -E "update.*version.*code.*name")

    echo "$filtered_commits"
}

# Categorize commits into user-facing improvements (legacy function - kept for fallback)
categorize_commits() {
    local commits="$1"

    # Use regular variables instead of associative arrays for compatibility
    local features=""
    local fixes=""
    local improvements=""
    local ui_ux=""

    while IFS= read -r commit; do
        if [[ -z "$commit" ]]; then continue; fi

        local commit_msg=$(echo "$commit" | sed 's/^[a-f0-9]* //')
        local commit_lower=$(echo "$commit_msg" | tr '[:upper:]' '[:lower:]')

        if [[ "$commit_lower" =~ ^(feat:|add:|implement:) ]]; then
            features+="$commit_msg"$'\n'
        elif [[ "$commit_lower" =~ ^(fix:|resolve:|correct:) ]]; then
            fixes+="$commit_msg"$'\n'
        elif [[ "$commit_lower" =~ ^(improve:|enhance:|optimize:) ]]; then
            improvements+="$commit_msg"$'\n'
        elif [[ "$commit_lower" =~ ^(ui:|ux:|design:) ]]; then
            ui_ux+="$commit_msg"$'\n'
        else
            # Default to improvements for uncategorized commits
            improvements+="$commit_msg"$'\n'
        fi
    done <<< "$commits"

    # Output categories in a parseable format
    echo "FEATURES:$features"
    echo "FIXES:$fixes"
    echo "IMPROVEMENTS:$improvements"
    echo "UI_UX:$ui_ux"
}

# Generate user-friendly descriptions from commit messages
generate_user_friendly_description() {
    local commit_msg="$1"

    # Remove ANSI color codes first
    local clean_msg=$(echo "$commit_msg" | sed 's/\x1b\[[0-9;]*m//g')

    # Remove common prefixes
    clean_msg=$(echo "$clean_msg" | sed -E 's/^(feat|fix|improve|enhance|optimize|ui|ux|design|add|implement|resolve|correct):\s*//')

    # Remove scope prefixes like (auth):, (client):, etc.
    clean_msg=$(echo "$clean_msg" | sed -E 's/^\([^)]+\):\s*//')

    # Capitalize first letter using awk
    clean_msg=$(echo "$clean_msg" | awk '{if(length($0)>0) print toupper(substr($0,1,1)) substr($0,2); else print $0}')

    # Ensure it ends with a period if it doesn't already
    if [[ ! "$clean_msg" =~ \.$|!$|\?$ ]]; then
        clean_msg="$clean_msg."
    fi

    echo "$clean_msg"
}

# Generate HTML list items for a category
generate_html_list_items() {
    local category_commits="$1"
    local html_items=""
    
    while IFS= read -r commit; do
        if [[ -n "$commit" ]]; then
            local description=$(generate_user_friendly_description "$commit")
            html_items+="                    <li>$description</li>"$'\n'
        fi
    done <<< "$category_commits"
    
    echo "$html_items"
}

# Get next version number based on pubspec.yaml
get_next_version() {
    local current_version="$1"
    local is_beta="$2"

    # If no current version provided, extract from pubspec.yaml
    if [[ -z "$current_version" ]]; then
        current_version=$(extract_current_version)
        if [[ $? -ne 0 ]]; then
            log_error "Could not extract current version"
            if [[ "$is_beta" == "true" ]]; then
                echo "1.0.0-beta"
            else
                echo "1.0.0"
            fi
            return
        fi
    fi

    # Clean version string (remove "Version " prefix if present)
    current_version=$(echo "$current_version" | sed -E 's/^Version\s*//')

    # Extract version components
    local major minor patch
    IFS='.' read -r major minor patch <<< "$current_version"

    # Increment patch version
    patch=$((patch + 1))

    if [[ "$is_beta" == "true" ]]; then
        echo "$major.$minor.$patch-beta"
    else
        echo "$major.$minor.$patch"
    fi
}

# Create HTML template for empty release file
create_html_template() {
    local file_path="$1"
    local is_beta="$2"
    local title="Release Notes"

    if [[ "$is_beta" == "true" ]]; then
        title="Beta Release Notes"
    fi

    cat > "$file_path" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - TITLE_PLACEHOLDER</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <section class="content">
        <div class="container">
            <h1>TITLE_PLACEHOLDER</h1>

            <!-- RELEASE_NOTES_PLACEHOLDER -->

        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load header and footer dynamically
        fetch('../includes/header.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('releases/index.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            });

        fetch('../includes/footer.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            });
    </script>
</body>
</html>
EOF

    # Replace placeholders
    sed -i.bak "s/TITLE_PLACEHOLDER/$title/g" "$file_path" && rm "$file_path.bak"
}

# Generate release notes HTML content
generate_release_notes_html() {
    local version="$1"
    local categorized_commits="$2"
    local timestamp="$3"

    local html_content=""
    local current_date=$(date "+%Y-%m-%d %H:%M:%S")

    # Add metadata comments
    html_content+="            <!-- LAST_UPDATED: $current_date -->"$'\n'
    html_content+="            <!-- LAST_VERSION: $version -->"$'\n'
    html_content+="            <p><em>Last updated: $current_date</em></p>"$'\n'
    html_content+=""$'\n'

    # Start release section
    html_content+="            <div class=\"release-section\">"$'\n'
    html_content+="                <h2>Version $version</h2>"$'\n'

    # Parse categorized commits
    local features fixes improvements ui_ux
    while IFS= read -r line; do
        if [[ "$line" =~ ^FEATURES: ]]; then
            features=$(echo "$line" | sed 's/^FEATURES://')
        elif [[ "$line" =~ ^FIXES: ]]; then
            fixes=$(echo "$line" | sed 's/^FIXES://')
        elif [[ "$line" =~ ^IMPROVEMENTS: ]]; then
            improvements=$(echo "$line" | sed 's/^IMPROVEMENTS://')
        elif [[ "$line" =~ ^UI_UX: ]]; then
            ui_ux=$(echo "$line" | sed 's/^UI_UX://')
        fi
    done <<< "$categorized_commits"

    # Add sections with content
    if [[ -n "$features" && "$features" != $'\n' ]]; then
        html_content+="                <h3>New Features</h3>"$'\n'
        html_content+="                <ul>"$'\n'
        html_content+="$(generate_html_list_items "$features")"
        html_content+="                </ul>"$'\n'
        html_content+=""$'\n'
    fi

    if [[ -n "$fixes" && "$fixes" != $'\n' ]]; then
        html_content+="                <h3>Bug Fixes</h3>"$'\n'
        html_content+="                <ul>"$'\n'
        html_content+="$(generate_html_list_items "$fixes")"
        html_content+="                </ul>"$'\n'
        html_content+=""$'\n'
    fi

    if [[ -n "$improvements" && "$improvements" != $'\n' ]]; then
        html_content+="                <h3>Improvements</h3>"$'\n'
        html_content+="                <ul>"$'\n'
        html_content+="$(generate_html_list_items "$improvements")"
        html_content+="                </ul>"$'\n'
        html_content+=""$'\n'
    fi

    if [[ -n "$ui_ux" && "$ui_ux" != $'\n' ]]; then
        html_content+="                <h3>UI/UX Changes</h3>"$'\n'
        html_content+="                <ul>"$'\n'
        html_content+="$(generate_html_list_items "$ui_ux")"
        html_content+="                </ul>"$'\n'
        html_content+=""$'\n'
    fi

    html_content+="            </div>"$'\n'
    html_content+=""$'\n'

    echo "$html_content"
}

# Convert AI-generated content to HTML format
convert_ai_content_to_html() {
    local version="$1"
    local ai_content="$2"
    local timestamp="$3"

    local html_content=""
    local current_date=$(date "+%Y-%m-%d %H:%M:%S")

    # Add metadata comments
    html_content+="            <!-- LAST_UPDATED: $current_date -->"$'\n'
    html_content+="            <!-- LAST_VERSION: $version -->"$'\n'
    html_content+="            <p><em>Last updated: $current_date</em></p>"$'\n'
    html_content+=""$'\n'

    # Start release section
    html_content+="            <div class=\"release-section\">"$'\n'
    html_content+="                <h2>Version $version</h2>"$'\n'

    # Convert AI-generated markdown-style content to HTML
    # This is a simple conversion - the AI should provide well-structured content
    local in_list=false
    local current_section=""

    while IFS= read -r line; do
        if [[ -z "$line" ]]; then
            # Empty line
            if [[ "$in_list" == true ]]; then
                html_content+="                </ul>"$'\n'
                html_content+=""$'\n'
                in_list=false
            fi
        elif [[ "$line" =~ ^#+[[:space:]]*(.*) ]]; then
            # Header line (### New Features, ## Bug Fixes, etc.)
            if [[ "$in_list" == true ]]; then
                html_content+="                </ul>"$'\n'
                in_list=false
            fi
            local header_text="${BASH_REMATCH[1]}"
            html_content+="                <h3>$header_text</h3>"$'\n'
            current_section="$header_text"
        elif [[ "$line" =~ ^[[:space:]]*-[[:space:]]*(.*) ]]; then
            # Bullet point
            if [[ "$in_list" == false ]]; then
                html_content+="                <ul>"$'\n'
                in_list=true
            fi
            local bullet_text="${BASH_REMATCH[1]}"
            html_content+="                    <li>$bullet_text</li>"$'\n'
        elif [[ "$line" =~ ^\*\*(.*?)\*\*:?(.*)$ ]]; then
            # Bold text like **New Features**: or **Bug Fixes**:
            if [[ "$in_list" == true ]]; then
                html_content+="                </ul>"$'\n'
                in_list=false
            fi
            local header_text="${BASH_REMATCH[1]}"
            local remaining_text="${BASH_REMATCH[2]}"
            html_content+="                <h3>$header_text</h3>"$'\n'
            if [[ -n "$remaining_text" && "$remaining_text" != ":" ]]; then
                html_content+="                <p>$remaining_text</p>"$'\n'
            fi
            current_section="$header_text"
        elif [[ -n "$line" ]]; then
            # Regular text line
            if [[ "$in_list" == true ]]; then
                html_content+="                </ul>"$'\n'
                in_list=false
            fi
            html_content+="                <p>$line</p>"$'\n'
        fi
    done <<< "$ai_content"

    # Close any open list
    if [[ "$in_list" == true ]]; then
        html_content+="                </ul>"$'\n'
    fi

    html_content+="            </div>"$'\n'
    html_content+=""$'\n'

    echo "$html_content"
}

# Update HTML file with new release notes
update_html_file() {
    local html_file="$1"
    local new_release_html="$2"
    local is_beta="$3"

    # Create file if it doesn't exist or is empty
    if [[ ! -f "$html_file" ]] || [[ ! -s "$html_file" ]]; then
        log_info "Creating new HTML file: $html_file"
        create_html_template "$html_file" "$is_beta"
    fi

    # Create backup
    cp "$html_file" "$html_file.backup"

    # Find insertion point (after the h1 tag and before existing release sections)
    local temp_file=$(mktemp)
    local in_content_section=false
    local inserted=false

    while IFS= read -r line; do
        echo "$line" >> "$temp_file"

        # Look for the h1 tag to know we're in the content section
        if [[ "$line" =~ \<h1\> ]] && [[ "$line" =~ Release\ Notes ]]; then
            in_content_section=true
        fi

        # Insert new release notes after h1 and before first release-section or at placeholder
        if [[ "$in_content_section" == true ]] && [[ "$inserted" == false ]]; then
            if [[ "$line" =~ RELEASE_NOTES_PLACEHOLDER ]] || [[ "$line" =~ \<div\ class=\"release-section\"\> ]]; then
                if [[ "$line" =~ RELEASE_NOTES_PLACEHOLDER ]]; then
                    # Replace placeholder
                    sed -i.tmp '$d' "$temp_file" && rm "$temp_file.tmp"
                    echo "$new_release_html" >> "$temp_file"
                else
                    # Insert before existing release section
                    sed -i.tmp '$d' "$temp_file" && rm "$temp_file.tmp"
                    echo "$new_release_html" >> "$temp_file"
                    echo "$line" >> "$temp_file"
                fi
                inserted=true
            fi
        fi
    done < "$html_file"

    # If we didn't find a good insertion point, append before closing container
    if [[ "$inserted" == false ]]; then
        log_warn "Could not find ideal insertion point, appending to end of content"
        sed -i.tmp '/^[[:space:]]*<\/div>.*container/i\
'"$new_release_html"'' "$temp_file" && rm "$temp_file.tmp"
    fi

    # Replace original file
    mv "$temp_file" "$html_file"

    log_info "Updated HTML file: $html_file"
    log_info "Backup created: $html_file.backup"
}

# Generate focused testing areas from commits
generate_testing_focus_areas() {
    local categorized_commits="$1"
    local focus_areas=()

    # Parse categorized commits
    local features fixes improvements ui_ux
    while IFS= read -r line; do
        if [[ "$line" =~ ^FEATURES: ]]; then
            features=$(echo "$line" | sed 's/^FEATURES://')
        elif [[ "$line" =~ ^FIXES: ]]; then
            fixes=$(echo "$line" | sed 's/^FIXES://')
        elif [[ "$line" =~ ^IMPROVEMENTS: ]]; then
            improvements=$(echo "$line" | sed 's/^IMPROVEMENTS://')
        elif [[ "$line" =~ ^UI_UX: ]]; then
            ui_ux=$(echo "$line" | sed 's/^UI_UX://')
        fi
    done <<< "$categorized_commits"

    # Generate focus areas based on content
    if [[ -n "$features" && "$features" != $'\n' ]]; then
        if echo "$features" | grep -qi "auth\|sign\|login"; then
            focus_areas+=("🔐 Test authentication flows (sign-in/out, account switching)")
        fi
        if echo "$features" | grep -qi "drag\|drop\|upload"; then
            focus_areas+=("📁 Test file upload and drag-and-drop functionality")
        fi
        if echo "$features" | grep -qi "desktop\|macos\|windows"; then
            focus_areas+=("💻 Test desktop platform features and window management")
        fi
        if echo "$features" | grep -qi "prompt\|execution\|llm"; then
            focus_areas+=("🤖 Test prompt execution and AI response handling")
        fi
    fi

    if [[ -n "$fixes" && "$fixes" != $'\n' ]]; then
        if echo "$fixes" | grep -qi "crash\|error\|exception"; then
            focus_areas+=("🐛 Test previously problematic areas for stability")
        fi
        if echo "$fixes" | grep -qi "sync\|connection\|network"; then
            focus_areas+=("🌐 Test network connectivity and data synchronization")
        fi
    fi

    if [[ -n "$improvements" && "$improvements" != $'\n' ]]; then
        if echo "$improvements" | grep -qi "performance\|speed\|optimization"; then
            focus_areas+=("⚡ Test app performance and response times")
        fi
        if echo "$improvements" | grep -qi "ui\|ux\|interface"; then
            focus_areas+=("🎨 Test user interface improvements and navigation")
        fi
    fi

    # If no specific areas found, add general testing areas
    if [[ ${#focus_areas[@]} -eq 0 ]]; then
        focus_areas+=("🔄 Test core app functionality and navigation")
        focus_areas+=("📱 Test on different devices and screen sizes")
        focus_areas+=("🔐 Verify authentication and account management")
    fi

    # Limit to 5 focus areas
    local max_areas=5
    if [[ ${#focus_areas[@]} -gt $max_areas ]]; then
        focus_areas=("${focus_areas[@]:0:$max_areas}")
    fi

    printf '%s\n' "${focus_areas[@]}"
}

# Generate WhatsApp beta testing message
generate_whatsapp_message() {
    local version="$1"
    local categorized_commits="$2"

    local message="🚀 *Promz Beta Update - Version $version*\n\n"
    message+="Hey beta testers! 👋\n\n"
    message+="We've just released a new beta version with exciting improvements. Your testing helps make Promz better for everyone!\n\n"

    # Get focused testing areas
    local focus_areas
    focus_areas=$(generate_testing_focus_areas "$categorized_commits")

    message+="🎯 *Priority Testing Areas:*\n"
    while IFS= read -r area; do
        if [[ -n "$area" ]]; then
            message+="$area\n"
        fi
    done <<< "$focus_areas"

    message+="\n💡 *Quick Testing Tips:*\n"
    message+="• Try different scenarios and edge cases\n"
    message+="• Test on your primary device/platform\n"
    message+="• Report any crashes or unexpected behavior\n\n"

    message+="🙏 *Thank you for being an amazing beta tester!*\n"
    message+="Your feedback drives our improvements. Found something? Let us know!\n\n"
    message+="Happy testing! 🧪✨"

    echo -e "$message"
}

# Convert AI-generated release notes to WhatsApp format
convert_release_notes_to_whatsapp() {
    local version="$1"
    local ai_content="$2"

    local message="🚀 *Promz Beta Update - Version $version*\n\n"
    message+="Hey beta testers! 👋\n\n"
    message+="We've just released a new beta version with exciting improvements. Your testing helps make Promz better for everyone!\n\n"

    # Extract key highlights from AI content for testing focus
    local focus_areas=()

    # Parse AI content to identify testing areas
    while IFS= read -r line; do
        if [[ "$line" =~ [Aa]uth|[Ss]ign|[Ll]ogin ]]; then
            focus_areas+=("🔐 Test authentication flows (sign-in/out, account switching)")
        elif [[ "$line" =~ [Dd]rag|[Dd]rop|[Uu]pload ]]; then
            focus_areas+=("📁 Test file upload and drag-and-drop functionality")
        elif [[ "$line" =~ [Dd]esktop|[Mm]acOS|[Ww]indows ]]; then
            focus_areas+=("💻 Test desktop platform features and window management")
        elif [[ "$line" =~ [Pp]rompt|[Ee]xecution|[Ll]lm ]]; then
            focus_areas+=("🤖 Test prompt execution and AI response handling")
        elif [[ "$line" =~ [Pp]erformance|[Ss]peed|[Oo]ptimization ]]; then
            focus_areas+=("⚡ Test app performance and response times")
        elif [[ "$line" =~ [Uu]i|[Uu]x|[Ii]nterface ]]; then
            focus_areas+=("🎨 Test user interface improvements and navigation")
        fi
    done <<< "$ai_content"

    # Remove duplicates and limit to 5 areas
    local unique_areas=()
    for area in "${focus_areas[@]}"; do
        local found=false
        for existing in "${unique_areas[@]}"; do
            if [[ "$area" == "$existing" ]]; then
                found=true
                break
            fi
        done
        if [[ "$found" == false ]]; then
            unique_areas+=("$area")
        fi
    done

    # Limit to 5 focus areas
    if [[ ${#unique_areas[@]} -gt 5 ]]; then
        unique_areas=("${unique_areas[@]:0:5}")
    fi

    # If no specific areas found, add general testing areas
    if [[ ${#unique_areas[@]} -eq 0 ]]; then
        unique_areas+=("🔄 Test core app functionality and navigation")
        unique_areas+=("📱 Test on different devices and screen sizes")
        unique_areas+=("🔐 Verify authentication and account management")
    fi

    message+="🎯 *Priority Testing Areas:*\n"
    for area in "${unique_areas[@]}"; do
        message+="$area\n"
    done

    message+="\n💡 *Quick Testing Tips:*\n"
    message+="• Try different scenarios and edge cases\n"
    message+="• Test on your primary device/platform\n"
    message+="• Report any crashes or unexpected behavior\n\n"

    message+="🙏 *Thank you for being an amazing beta tester!*\n"
    message+="Your feedback drives our improvements. Found something? Let us know!\n\n"
    message+="Happy testing! 🧪✨"

    echo -e "$message"
}

# Generate email content for beta users
generate_email_message() {
    local version="$1"
    local categorized_commits="$2"

    local email_content=""

    # Subject line
    email_content+="Subject: Promz Beta $version - New Testing Opportunities Available\n\n"

    # Email body
    email_content+="Dear Beta Tester,\n\n"
    email_content+="We hope this message finds you well. We're excited to announce the release of Promz Beta Version $version, and we'd love your help in testing the latest improvements.\n\n"

    email_content+="## What's New in This Release\n\n"

    # Parse categorized commits for email format
    local features fixes improvements ui_ux
    while IFS= read -r line; do
        if [[ "$line" =~ ^FEATURES: ]]; then
            features=$(echo "$line" | sed 's/^FEATURES://')
        elif [[ "$line" =~ ^FIXES: ]]; then
            fixes=$(echo "$line" | sed 's/^FIXES://')
        elif [[ "$line" =~ ^IMPROVEMENTS: ]]; then
            improvements=$(echo "$line" | sed 's/^IMPROVEMENTS://')
        elif [[ "$line" =~ ^UI_UX: ]]; then
            ui_ux=$(echo "$line" | sed 's/^UI_UX://')
        fi
    done <<< "$categorized_commits"

    # Add key highlights (limit to 3-4 most important items)
    local highlight_count=0
    local max_highlights=4

    if [[ -n "$features" && "$features" != $'\n' && $highlight_count -lt $max_highlights ]]; then
        email_content+="### ✨ New Features\n"
        while IFS= read -r commit && [[ $highlight_count -lt $max_highlights ]]; do
            if [[ -n "$commit" ]]; then
                local description=$(generate_user_friendly_description "$commit")
                email_content+="- $description\n"
                ((highlight_count++))
            fi
        done <<< "$features"
        email_content+="\n"
    fi

    if [[ -n "$fixes" && "$fixes" != $'\n' && $highlight_count -lt $max_highlights ]]; then
        email_content+="### 🐛 Key Bug Fixes\n"
        while IFS= read -r commit && [[ $highlight_count -lt $max_highlights ]]; do
            if [[ -n "$commit" ]]; then
                local description=$(generate_user_friendly_description "$commit")
                email_content+="- $description\n"
                ((highlight_count++))
            fi
        done <<< "$fixes"
        email_content+="\n"
    fi

    if [[ -n "$improvements" && "$improvements" != $'\n' && $highlight_count -lt $max_highlights ]]; then
        email_content+="### ⚡ Performance Improvements\n"
        while IFS= read -r commit && [[ $highlight_count -lt $max_highlights ]]; do
            if [[ -n "$commit" ]]; then
                local description=$(generate_user_friendly_description "$commit")
                email_content+="- $description\n"
                ((highlight_count++))
            fi
        done <<< "$improvements"
        email_content+="\n"
    fi

    # Testing focus areas
    email_content+="## 🎯 Priority Testing Areas\n\n"
    email_content+="To help us ensure the highest quality release, we'd appreciate your focus on these key areas:\n\n"

    local focus_areas
    focus_areas=$(generate_testing_focus_areas "$categorized_commits")

    while IFS= read -r area; do
        if [[ -n "$area" ]]; then
            # Convert emoji bullets to numbered list for email
            local clean_area=$(echo "$area" | sed 's/^[^[:space:]]*[[:space:]]*//')
            email_content+="- $clean_area\n"
        fi
    done <<< "$focus_areas"

    email_content+="\n## 📋 Testing Guidelines\n\n"
    email_content+="**What to Test:**\n"
    email_content+="- Core functionality in your typical usage scenarios\n"
    email_content+="- Edge cases and unusual workflows\n"
    email_content+="- Performance under different conditions\n"
    email_content+="- Cross-platform compatibility (if applicable)\n\n"

    email_content+="**How to Report Issues:**\n"
    email_content+="- Use the in-app feedback feature for quick reports\n"
    email_content+="- Include steps to reproduce any issues\n"
    email_content+="- Note your device/platform details\n"
    email_content+="- Screenshots or screen recordings are always helpful\n\n"

    email_content+="## 🙏 Thank You\n\n"
    email_content+="Your participation in our beta program is invaluable. Every bug report, suggestion, and piece of feedback helps us create a better experience for all Promz users.\n\n"

    email_content+="We truly appreciate the time and effort you invest in testing. Your contributions directly impact the quality and reliability of our final releases.\n\n"

    email_content+="Happy testing, and thank you for being part of the Promz community!\n\n"
    email_content+="Best regards,\n"
    email_content+="The Promz Development Team\n\n"
    email_content+="---\n"
    email_content+="P.S. Have feedback about our beta testing process? We'd love to hear that too!"

    echo -e "$email_content"
}

# Check if Promz server is available and ready for gRPC calls
check_server_availability() {
    local server_host="localhost"
    local server_port="8080"
    local health_url="http://${server_host}:${server_port}/health"

    # Check if grpcurl is available
    if ! command -v grpcurl > /dev/null 2>&1; then
        log_debug "grpcurl not available for gRPC calls"
        return 1
    fi

    # Check if server is running using health endpoint
    log_debug "Checking server health at $health_url..."
    local health_response
    if health_response=$(curl -s --connect-timeout 3 --max-time 5 "$health_url" 2>/dev/null); then
        # Parse health response
        if echo "$health_response" | grep -q '"status":"ok"'; then
            log_debug "Server health check passed"

            # Additional check: verify gRPC DevToolsService is available by making a test call
            log_debug "Verifying gRPC DevToolsService availability..."
            local test_request='{"content_text": "test", "content_type": "release_notes"}'
            local proto_path="$WORKSPACE_ROOT/api/proto/v1/dev_tools.proto"
            if [[ -f "$proto_path" ]] && echo "$test_request" | grpcurl -plaintext -connect-timeout 3 -max-time 5 \
                -import-path "$WORKSPACE_ROOT" -proto "$proto_path" \
                -d @ "$server_host:$server_port" \
                promz.api.v1.DevToolsService/OptimizeReleaseContent > /dev/null 2>&1; then
                log_debug "gRPC DevToolsService is available"
                return 0
            else
                log_debug "gRPC DevToolsService not available"
                return 1
            fi
        else
            log_debug "Server health check failed: unexpected response"
            return 1
        fi
    else
        log_debug "Server health check failed: no response"
        return 1
    fi
}

# Escape JSON string content properly
escape_json_string() {
    local input="$1"
    # Escape backslashes first, then quotes, then newlines, tabs, and other control characters
    echo "$input" | sed 's/\\/\\\\/g' | sed 's/"/\\"/g' | sed 's/$/\\n/' | tr -d '\n' | sed 's/\\n$//'
}

# Content optimization using gRPC service (if available)
optimize_content_with_llm() {
    local content="$1"
    local content_type="$2"  # "whatsapp", "email", or "release_notes"

    # Check server availability first
    if ! check_server_availability; then
        log_debug "Promz server not available for gRPC optimization"
        return 1
    fi

    log_info "Attempting to optimize content using LLM service..."

    # Create temporary files for the request and response
    local temp_request=$(mktemp)
    local temp_response=$(mktemp)
    local temp_error=$(mktemp)

    # Properly escape content for JSON
    local escaped_content
    escaped_content=$(escape_json_string "$content")

    # Create gRPC request JSON for OptimizeReleaseContent
    cat > "$temp_request" << EOF
{
  "content_text": "$escaped_content",
  "content_type": "$content_type"
}
EOF

    log_debug "Making gRPC call to OptimizeReleaseContent..."

    # Make gRPC call using grpcurl with proper error handling
    local proto_path="$WORKSPACE_ROOT/api/proto/v1/dev_tools.proto"
    if [[ -f "$proto_path" ]] && cat "$temp_request" | grpcurl -plaintext -connect-timeout 10 -max-time 30 \
        -import-path "$WORKSPACE_ROOT" -proto "$proto_path" \
        -d @ \
        localhost:8080 \
        promz.api.v1.DevToolsService/OptimizeReleaseContent \
        > "$temp_response" 2> "$temp_error"; then

        log_debug "gRPC call completed successfully"

        # Check if there's an error in the response
        local error_message
        error_message=$(jq -r '.error.message // empty' "$temp_response" 2>/dev/null)

        if [[ -n "$error_message" && "$error_message" != "null" ]]; then
            log_warn "LLM optimization error: $error_message"

            # Check for development environment restriction
            if [[ "$error_message" == *"development environment"* ]]; then
                log_warn "Content optimization is only available in development environment"
                log_warn "Set ENVIRONMENT=development or DEBUG=true to enable this feature"
            fi

            # Clean up and return failure
            rm -f "$temp_request" "$temp_response" "$temp_error"
            return 1
        else
            # Extract optimized content from response
            # Try both snake_case (proto field name) and camelCase (JSON serialization)
            local optimized_content
            optimized_content=$(jq -r '.optimized_content // .optimizedContent // empty' "$temp_response" 2>/dev/null)

            if [[ -n "$optimized_content" && "$optimized_content" != "null" ]]; then
                log_info "Content successfully optimized using LLM"
                echo "$optimized_content"

                # Clean up and return success
                rm -f "$temp_request" "$temp_response" "$temp_error"
                return 0
            else
                log_warn "LLM optimization returned empty result"
                rm -f "$temp_request" "$temp_response" "$temp_error"
                return 1
            fi
        fi
    else
        # gRPC call failed
        local error_output
        error_output=$(cat "$temp_error" 2>/dev/null)

        if [[ -n "$error_output" ]]; then
            log_warn "gRPC call failed: $error_output"
        else
            log_warn "gRPC call failed with unknown error"
        fi

        # Clean up and return failure
        rm -f "$temp_request" "$temp_response" "$temp_error"
        return 1
    fi
}

# Alternative content optimization using built-in text processing
optimize_content_builtin() {
    local content="$1"
    local content_type="$2"

    log_info "Applying built-in content optimization..."

    # Basic text improvements
    local optimized="$content"

    # Remove excessive whitespace
    optimized=$(echo "$optimized" | sed 's/[[:space:]]\+/ /g' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

    # Improve sentence structure (basic)
    optimized=$(echo "$optimized" | sed 's/\. \([a-z]\)/. \U\1/g')  # Capitalize after periods

    # For WhatsApp messages, ensure emoji spacing
    if [[ "$content_type" == "whatsapp" ]]; then
        optimized=$(echo "$optimized" | sed 's/\([🚀🎯💡🙏🧪✨🐛⚡🎨🔐📁💻🤖🌐🔄📱]\)\([A-Za-z]\)/\1 \2/g')
    fi

    echo "$optimized"
}

# Generate comprehensive release notes using AI from raw commits
generate_ai_release_notes() {
    local raw_commits="$1"
    local next_version="$2"

    # Prepare raw commit data for AI processing
    local raw_content="Generate comprehensive release notes for Version $next_version from the following Git commits:\n\n"
    raw_content+="$raw_commits"

    # Send raw commits to AI for comprehensive processing
    log_debug "Sending $(echo "$raw_commits" | wc -l) raw commits to AI for comprehensive analysis"

    # Send raw commits to AI for complete processing
    local ai_release_notes
    ai_release_notes=$(optimize_content_with_llm "$raw_content" "release_notes")
    local llm_result=$?

    if [[ $llm_result -eq 0 && -n "$ai_release_notes" ]]; then
        echo "$ai_release_notes"
        return 0
    else
        log_warn "AI processing failed, falling back to basic formatting"
        # Fallback: basic formatting of raw commits
        local fallback_content="Release Notes for Version $next_version\n\n"
        fallback_content+="Recent Changes:\n"
        while IFS= read -r commit; do
            if [[ -n "$commit" ]]; then
                local commit_msg=$(echo "$commit" | sed 's/^[a-f0-9]* //')
                local description=$(generate_user_friendly_description "$commit_msg")
                fallback_content+="- $description\n"
            fi
        done <<< "$raw_commits"
        echo -e "$fallback_content"
        return 1
    fi
}

# Main function to generate release notes
generate_release_notes() {
    local target_file="$1"
    local is_beta="$2"
    local release_type="Beta"

    if [[ "$is_beta" != "true" ]]; then
        release_type="Production"
    fi

    log_info "Generating $release_type release notes..."

    # Extract last release information
    local release_info=$(extract_last_release_info "$target_file")
    local last_date=$(echo "$release_info" | cut -d'|' -f1)
    local last_version=$(echo "$release_info" | cut -d'|' -f2)

    log_debug "Last release date: $last_date"
    log_debug "Last release version: $last_version"

    # Get commits since last release
    local all_commits=$(get_commits_since_last_release "$last_date" "$last_version")

    if [[ -z "$all_commits" ]]; then
        log_warn "No commits found since last release"
        echo -e "${YELLOW}No new commits to generate release notes from.${NC}"
        return 0
    fi

    log_debug "Found $(echo "$all_commits" | wc -l) total commits"

    # Filter to user-facing commits
    local user_commits=$(filter_user_facing_commits "$all_commits")

    if [[ -z "$user_commits" ]]; then
        log_warn "No user-facing commits found"
        echo -e "${YELLOW}No user-facing changes found to generate release notes from.${NC}"
        return 0
    fi

    log_debug "Found $(echo "$user_commits" | wc -l) user-facing commits"

    # Generate next version based on pubspec.yaml
    local next_version=$(get_next_version "" "$is_beta")

    log_info "Generating release notes for version: $next_version"

    # Ask if user wants to use AI for comprehensive release notes generation
    echo ""
    read -rp "Generate comprehensive release notes using AI? (Y/n): " ai_confirm
    local use_ai_notes=""
    local ai_generated_content=""

    if [[ ! "$ai_confirm" =~ ^[Nn]$ ]]; then
        log_info "Generating comprehensive release notes using AI..."

        # Use AI to process ALL commits (not just user-facing filtered ones)
        # This allows AI to make better filtering and categorization decisions
        ai_generated_content=$(generate_ai_release_notes "$all_commits" "$next_version")
        local ai_result=$?

        if [[ $ai_result -eq 0 ]]; then
            log_info "Successfully generated comprehensive release notes using AI"
            # Show AI-generated preview
            echo -e "\n${CYAN}=== AI-GENERATED RELEASE NOTES PREVIEW ===${NC}"
            echo -e "$ai_generated_content"
            echo -e "\n${CYAN}=============================================${NC}"

            echo ""
            read -rp "Use the AI-generated content? (Y/n): " use_ai_confirm
            if [[ ! "$use_ai_confirm" =~ ^[Nn]$ ]]; then
                use_ai_notes="true"
                log_info "Using AI-generated release notes"
            else
                log_info "Using traditional categorized approach"
            fi
        else
            log_warn "AI generation failed, using traditional approach"
        fi
    else
        log_info "Using traditional categorized approach"
    fi

    # Generate HTML content - use AI content if available, otherwise fall back to traditional approach
    local current_timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    local release_html=""

    if [[ "$use_ai_notes" == "true" ]]; then
        # Convert AI-generated content to HTML format
        release_html=$(convert_ai_content_to_html "$next_version" "$ai_generated_content" "$current_timestamp")
    else
        # Traditional approach: categorize commits and generate HTML
        local categorized=$(categorize_commits "$user_commits")
        release_html=$(generate_release_notes_html "$next_version" "$categorized" "$current_timestamp")
    fi

    # Show preview if not already shown
    if [[ "$optimize_confirm" != [Yy] ]]; then
        echo -e "\n${CYAN}=== RELEASE NOTES PREVIEW ===${NC}"
        echo -e "${BLUE}Version:${NC} $next_version"
        echo -e "${BLUE}Type:${NC} $release_type"
        echo -e "${BLUE}Target File:${NC} $target_file"
        echo ""

        # Show categorized commits for preview
        echo -e "${YELLOW}Changes to be included:${NC}"
        while IFS= read -r commit; do
            if [[ -n "$commit" && ! "$commit" =~ ^\[.*\] ]]; then
                local commit_msg=$(echo "$commit" | sed 's/^[a-f0-9]* //')
                echo "  • $(generate_user_friendly_description "$commit_msg")"
            fi
        done <<< "$user_commits"

        echo -e "\n${CYAN}================================${NC}"
    fi

    # Confirm with user
    echo ""
    read -rp "Do you want to proceed with updating the HTML file? (y/N): " confirm

    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        update_html_file "$target_file" "$release_html" "$is_beta"
        log_info "$release_type release notes generated successfully!"

        # Show file location
        echo -e "\n${GREEN}Release notes updated in:${NC} $target_file"
        echo -e "${GREEN}Backup created:${NC} $target_file.backup"
        
        # Extract build number from pubspec.yaml
        local build_number=$(grep "^version:" "$PUBSPEC_FILE" | sed 's/^version:[^+]*+//')
        
        # Update versions registry
        echo -e "\n${BLUE}Updating versions registry...${NC}"
        if [[ -x "$UPDATE_REGISTRY_SCRIPT" ]]; then
            "$UPDATE_REGISTRY_SCRIPT" "$next_version" "$build_number" "$is_beta"
            if [[ $? -eq 0 ]]; then
                echo -e "${GREEN}✓ Versions registry updated successfully${NC}"
                echo -e "${GREEN}✓ Release notes will now correctly redirect based on version${NC}"
            else
                echo -e "${RED}✗ Failed to update versions registry${NC}"
                echo -e "${YELLOW}  You may need to manually update $VERSIONS_REGISTRY${NC}"
            fi
        else
            echo -e "${YELLOW}⚠ Update registry script not found or not executable${NC}"
            echo -e "${YELLOW}  Please run: chmod +x $UPDATE_REGISTRY_SCRIPT${NC}"
        fi
    else
        log_info "Operation cancelled by user"
    fi
}

# Generate WhatsApp message
generate_whatsapp_beta_message() {
    log_info "Generating WhatsApp message for beta users..."

    # Extract last release information from beta file
    local release_info=$(extract_last_release_info "$BETA_RELEASE_FILE")
    local last_date=$(echo "$release_info" | cut -d'|' -f1)
    local last_version=$(echo "$release_info" | cut -d'|' -f2)

    log_debug "Extracted release info: '$release_info'"
    log_debug "Extracted date: '$last_date'"
    log_debug "Extracted version: '$last_version'"

    # Get commits since last release
    local all_commits=$(get_commits_since_last_release "$last_date" "$last_version")

    if [[ -z "$all_commits" ]]; then
        log_warn "No commits found since last release"
        echo -e "${YELLOW}No new commits to generate WhatsApp message from.${NC}"
        return 0
    fi

    # Generate next version based on pubspec.yaml
    local next_version=$(get_next_version "" "true")

    # Generate WhatsApp message using AI with raw commits for better analysis
    local whatsapp_msg=""

    # First try to generate comprehensive content using AI
    local ai_content=$(generate_ai_release_notes "$all_commits" "$next_version")
    local ai_result=$?

    if [[ $ai_result -eq 0 ]]; then
        # Convert AI-generated release notes to WhatsApp format
        whatsapp_msg=$(convert_release_notes_to_whatsapp "$next_version" "$ai_content")
    else
        # Fallback to traditional approach
        local user_commits=$(filter_user_facing_commits "$all_commits")
        if [[ -z "$user_commits" ]]; then
            log_warn "No user-facing commits found"
            echo -e "${YELLOW}No user-facing changes found to generate WhatsApp message from.${NC}"
            return 0
        fi
        local categorized=$(categorize_commits "$user_commits")
        whatsapp_msg=$(generate_whatsapp_message "$next_version" "$categorized")
    fi

    # Ask if user wants to optimize content
    echo ""
    read -rp "Optimize content using AI? (y/N): " optimize_confirm
    if [[ "$optimize_confirm" =~ ^[Yy]$ ]]; then
        log_info "Optimizing WhatsApp message content..."

        # Try LLM optimization first
        local optimized_msg
        optimized_msg=$(optimize_content_with_llm "$whatsapp_msg" "whatsapp")
        local llm_result=$?

        if [[ $llm_result -eq 0 && -n "$optimized_msg" ]]; then
            whatsapp_msg="$optimized_msg"
            log_info "Successfully optimized content using AI"
        else
            log_warn "AI optimization failed, applying built-in optimization..."
            whatsapp_msg=$(optimize_content_builtin "$whatsapp_msg" "whatsapp")
        fi
    fi

    # Display message
    echo -e "\n${CYAN}=== WHATSAPP MESSAGE FOR BETA USERS ===${NC}"
    echo -e "$whatsapp_msg"
    echo -e "\n${CYAN}===========================================${NC}"

    # Option to copy to clipboard (if available)
    if command -v pbcopy > /dev/null 2>&1; then
        echo ""
        read -rp "Copy message to clipboard? (y/N): " copy_confirm
        if [[ "$copy_confirm" =~ ^[Yy]$ ]]; then
            echo -e "$whatsapp_msg" | pbcopy
            log_info "Message copied to clipboard!"
        fi
    fi
}

# Generate email for beta users
generate_email_beta_message() {
    log_info "Generating email content for beta users..."

    # Extract last release information from beta file
    local release_info=$(extract_last_release_info "$BETA_RELEASE_FILE")
    local last_date=$(echo "$release_info" | cut -d'|' -f1)
    local last_version=$(echo "$release_info" | cut -d'|' -f2)

    # Get commits since last release
    local all_commits=$(get_commits_since_last_release "$last_date" "$last_version")

    if [[ -z "$all_commits" ]]; then
        log_warn "No commits found since last release"
        echo -e "${YELLOW}No new commits to generate email content from.${NC}"
        return 0
    fi

    # Filter to user-facing commits
    local user_commits=$(filter_user_facing_commits "$all_commits")

    if [[ -z "$user_commits" ]]; then
        log_warn "No user-facing commits found"
        echo -e "${YELLOW}No user-facing changes found to generate email content from.${NC}"
        return 0
    fi

    # Categorize commits
    local categorized=$(categorize_commits "$user_commits")

    # Generate next version based on pubspec.yaml
    local next_version=$(get_next_version "" "true")

    # Generate email content
    local email_content=$(generate_email_message "$next_version" "$categorized")

    # Ask if user wants to optimize content
    echo ""
    read -rp "Optimize email content using AI? (y/N): " optimize_confirm
    if [[ "$optimize_confirm" =~ ^[Yy]$ ]]; then
        log_info "Optimizing email content..."

        # Try LLM optimization first
        local optimized_email
        optimized_email=$(optimize_content_with_llm "$email_content" "email")
        local llm_result=$?

        if [[ $llm_result -eq 0 && -n "$optimized_email" ]]; then
            email_content="$optimized_email"
            log_info "Successfully optimized content using AI"
        else
            log_warn "AI optimization failed, applying built-in optimization..."
            email_content=$(optimize_content_builtin "$email_content" "email")
        fi
    fi

    # Display email content
    echo -e "\n${CYAN}=== EMAIL CONTENT FOR BETA USERS ===${NC}"
    echo -e "$email_content"
    echo -e "\n${CYAN}====================================${NC}"

    # Option to save to file
    echo ""
    read -rp "Save email content to file? (y/N): " save_confirm
    if [[ "$save_confirm" =~ ^[Yy]$ ]]; then
        local email_file="$WEB_DIR/beta_email_v${next_version}.txt"
        echo -e "$email_content" > "$email_file"
        log_info "Email content saved to: $email_file"
    fi

    # Option to copy to clipboard (if available)
    if command -v pbcopy > /dev/null 2>&1; then
        echo ""
        read -rp "Copy email content to clipboard? (y/N): " copy_confirm
        if [[ "$copy_confirm" =~ ^[Yy]$ ]]; then
            echo -e "$email_content" | pbcopy
            log_info "Email content copied to clipboard!"
        fi
    fi
}

# Show server status and gRPC availability
show_server_status() {
    echo -e "\n${BLUE}=== SERVER STATUS ===${NC}"

    # Check server availability
    if check_server_availability; then
        echo -e "${GREEN}✅ Promz server is running and gRPC service is available${NC}"
        echo -e "${GREEN}   Server: localhost:8080${NC}"
        echo -e "${GREEN}   gRPC LlmService: Available${NC}"
        echo -e "${GREEN}   Content optimization: Ready${NC}"
    else
        echo -e "${RED}❌ Promz server or gRPC service not available${NC}"
        echo -e "${YELLOW}   To enable AI content optimization:${NC}"
        echo -e "${YELLOW}   1. Start the Promz server (run_api.sh or similar)${NC}"
        echo -e "${YELLOW}   2. Ensure server is in development mode:${NC}"
        echo -e "${YELLOW}      - Set ENVIRONMENT=development${NC}"
        echo -e "${YELLOW}      - Or set DEBUG=true${NC}"
        echo -e "${YELLOW}      - Or set LOCAL_DEV=true${NC}"
        echo -e "${YELLOW}   3. Install grpcurl if not available:${NC}"
        echo -e "${YELLOW}      - macOS: brew install grpcurl${NC}"
        echo -e "${YELLOW}      - Other: https://github.com/fullstorydev/grpcurl${NC}"
    fi

    # Check grpcurl availability
    echo -e "\n${BLUE}=== TOOLS STATUS ===${NC}"
    if command -v grpcurl > /dev/null 2>&1; then
        echo -e "${GREEN}✅ grpcurl is available${NC}"
        grpcurl --version 2>/dev/null || echo -e "${GREEN}   grpcurl installed${NC}"
    else
        echo -e "${RED}❌ grpcurl not found${NC}"
        echo -e "${YELLOW}   Install with: brew install grpcurl${NC}"
    fi

    if command -v jq > /dev/null 2>&1; then
        echo -e "${GREEN}✅ jq is available${NC}"
    else
        echo -e "${YELLOW}⚠️  jq not found (recommended for better JSON parsing)${NC}"
        echo -e "${YELLOW}   Install with: brew install jq${NC}"
    fi
}

# Interactive menu system
show_menu() {
    set_colors

    while true; do
        echo -e "\n${CYAN}Promz Release Notes Generator${NC}"
        echo -e "${BLUE}================================${NC}"
        echo "0. Exit"
        echo "1. Generate Beta Release Notes"
        echo "2. Generate WhatsApp Message for Beta Users"
        echo "3. Generate Email for Beta Users"
        echo "4. Generate Production Release Notes"
        echo "5. Show Current Release Information"
        echo "6. Show Server Status & AI Optimization"
        echo -e "${BLUE}================================${NC}"

        read -rp "Enter your choice (0-6): " choice

        case $choice in
            0)
                echo -e "${GREEN}Goodbye!${NC}"
                exit 0
                ;;
            1)
                echo -e "\n${YELLOW}Generating Beta Release Notes...${NC}"
                generate_release_notes "$BETA_RELEASE_FILE" "true"
                ;;
            2)
                echo -e "\n${YELLOW}Generating WhatsApp Message for Beta Users...${NC}"
                generate_whatsapp_beta_message
                ;;
            3)
                echo -e "\n${YELLOW}Generating Email for Beta Users...${NC}"
                generate_email_beta_message
                ;;
            4)
                echo -e "\n${YELLOW}Generating Production Release Notes...${NC}"
                generate_release_notes "$PROD_RELEASE_FILE" "false"
                ;;
            5)
                echo -e "\n${YELLOW}Current Release Information:${NC}"
                show_current_release_info
                ;;
            6)
                echo -e "\n${YELLOW}Server Status & AI Optimization:${NC}"
                show_server_status
                ;;
            *)
                echo -e "${RED}Invalid choice. Please enter 0-6.${NC}"
                ;;
        esac

        # Pause before showing menu again
        echo ""
        read -rp "Press Enter to continue..."
    done
}

# Show current release information
show_current_release_info() {
    echo -e "\n${BLUE}=== CURRENT VERSION INFO ===${NC}"
    local current_version=$(extract_current_version)
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}Current Version (pubspec.yaml):${NC} $current_version"
        local next_beta=$(get_next_version "" "true")
        local next_prod=$(get_next_version "" "false")
        echo -e "${GREEN}Next Beta Version:${NC} $next_beta"
        echo -e "${GREEN}Next Production Version:${NC} $next_prod"
    else
        echo -e "${RED}Could not extract version from pubspec.yaml${NC}"
    fi

    echo -e "\n${BLUE}=== BETA RELEASE INFO ===${NC}"
    if [[ -f "$BETA_RELEASE_FILE" ]]; then
        local beta_info=$(extract_last_release_info "$BETA_RELEASE_FILE")
        local beta_date=$(echo "$beta_info" | cut -d'|' -f1)
        local beta_version=$(echo "$beta_info" | cut -d'|' -f2)

        echo -e "${GREEN}File:${NC} $BETA_RELEASE_FILE"
        echo -e "${GREEN}Last Version:${NC} ${beta_version:-"Not found"}"
        echo -e "${GREEN}Last Updated:${NC} ${beta_date:-"Not found"}"
    else
        echo -e "${RED}Beta release file not found${NC}"
    fi

    echo -e "\n${BLUE}=== PRODUCTION RELEASE INFO ===${NC}"
    if [[ -f "$PROD_RELEASE_FILE" ]]; then
        local prod_info=$(extract_last_release_info "$PROD_RELEASE_FILE")
        local prod_date=$(echo "$prod_info" | cut -d'|' -f1)
        local prod_version=$(echo "$prod_info" | cut -d'|' -f2)

        echo -e "${GREEN}File:${NC} $PROD_RELEASE_FILE"
        echo -e "${GREEN}Last Version:${NC} ${prod_version:-"Not found"}"
        echo -e "${GREEN}Last Updated:${NC} ${prod_date:-"Not found"}"
    else
        echo -e "${RED}Production release file not found${NC}"
    fi

    # Show recent commits
    echo -e "\n${BLUE}=== RECENT COMMITS (Last 10) ===${NC}"
    git log --oneline -10 --no-merges
}

# Main execution
main() {
    # Change to workspace root directory
    cd "$WORKSPACE_ROOT" || {
        log_error "Could not change to workspace root: $WORKSPACE_ROOT"
        exit 1
    }

    log_info "Starting Release Notes Generator"
    log_debug "Workspace root: $WORKSPACE_ROOT"
    log_debug "Web directory: $WEB_DIR"

    # Check prerequisites
    if ! check_prerequisites; then
        log_error "Prerequisites check failed"
        exit 1
    fi

    # Show interactive menu
    show_menu
}

# Execute main function
main "$@"
