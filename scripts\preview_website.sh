#!/bin/bash
# Script to preview the website locally before deployment
# This script starts a local HTTP server to render the web pages

# Default parameters
PORT=8000
SOURCE_DIRECTORY_RELATIVE=".." # Relative path from this script to the web source directory
OPEN_BROWSER=true

# Color definitions for better readability
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --port=*)
      PORT="${1#*=}"
      shift
      ;;
    --source-dir=*)
      SOURCE_DIRECTORY_RELATIVE="${1#*=}"
      shift
      ;;
    --no-browser)
      OPEN_BROWSER=false
      shift
      ;;
    --help)
      echo -e "${BLUE}Promz Website Preview Script${NC}"
      echo "Usage: $0 [options]"
      echo ""
      echo "Options:"
      echo "  --port=PORT          Set the HTTP server port (default: 8000)"
      echo "  --source-dir=DIR     Set the source directory relative to script (default: ..)"
      echo "  --no-browser         Don't automatically open browser"
      echo "  --help               Show this help message"
      exit 0
      ;;
    *)
      echo -e "${RED}Unknown parameter: $1${NC}"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Function to check if a command exists
function command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Get the absolute path of the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SOURCE_PATH="$(cd "$SCRIPT_DIR/$SOURCE_DIRECTORY_RELATIVE" && pwd)"

# Check if the source directory exists
if [ ! -d "$SOURCE_PATH" ]; then
  echo -e "${RED}ERROR: Source directory not found: $SOURCE_PATH${NC}"
  exit 1
fi

echo -e "${BLUE}Starting local preview server for website...${NC}"
echo "Source Directory: $SOURCE_PATH"
echo "Port: $PORT"

# Determine which HTTP server to use
if command_exists python3; then
  echo -e "${GREEN}Using Python 3 HTTP server${NC}"
  SERVER_CMD="python3 -m http.server $PORT"
elif command_exists python; then
  # Check Python version
  PYTHON_VERSION=$(python --version 2>&1)
  if [[ "$PYTHON_VERSION" == *"Python 3"* ]]; then
    echo -e "${GREEN}Using Python 3 HTTP server${NC}"
    SERVER_CMD="python -m http.server $PORT"
  else
    echo -e "${GREEN}Using Python 2 SimpleHTTPServer${NC}"
    SERVER_CMD="python -m SimpleHTTPServer $PORT"
  fi
else
  echo -e "${RED}ERROR: Python is not installed. Please install Python to use this script.${NC}"
  exit 1
fi

# Open browser if requested
if [ "$OPEN_BROWSER" = true ]; then
  echo -e "${BLUE}Opening browser...${NC}"
  if [[ "$(uname)" == "Darwin" ]]; then  # macOS
    (sleep 1 && open "http://localhost:$PORT") &
  elif [[ "$(uname)" == "Linux" ]]; then  # Linux
    (sleep 1 && xdg-open "http://localhost:$PORT") &
  fi
fi

# Print URLs for different sections
echo -e "\n${BLUE}Preview URLs:${NC}"
echo "Main website:       http://localhost:$PORT/"
echo "Release notes:      http://localhost:$PORT/releases/"
echo "Beta release notes: http://localhost:$PORT/beta/releases/"
echo -e "\n${YELLOW}Press Ctrl+C to stop the server${NC}\n"

# Start the HTTP server in the source directory
cd "$SOURCE_PATH" && eval "$SERVER_CMD"
