/**
 * Deep link handler for promz.ai
 * 
 * This script handles deep links in the format https://www.promz.ai/p/<id>
 * It detects the user's device and either:
 * 1. Redirects mobile users to the app store if the app isn't installed
 * 2. Shows desktop users a download page with information about the app
 */

(function() {
  // Debug flag to help with troubleshooting
  const DEBUG = true;
  
  function debugLog(message) {
    if (DEBUG) console.log(`[DeepLink] ${message}`);
  }

  // App store URLs
  const APP_STORE_URL = 'https://apps.apple.com/app/promz/idXXXXXXXXXX'; // Replace with actual App Store ID
  const PLAY_STORE_URL = 'https://play.google.com/apps/testing/ai.promz';
  const WINDOWS_STORE_URL = 'https://www.microsoft.com/store/apps/XXXXXXXXX'; // Replace with actual Microsoft Store ID
  
  // Main website URL
  const WEBSITE_URL = 'https://www.promz.ai';
  
  // Extract the short ID from the URL path
  function getShortId() {
    const path = window.location.pathname;
    debugLog('Getting short ID from path: ' + path);
    
    if (path === '/' || !path) return null;
    
    // Check for the /p/ prefix format (https://www.promz.ai/p/AAYy8i)
    if (path.startsWith('/p/')) {
      const id = path.substring(3);
      debugLog('Extracted ID from /p/ path: ' + id);
      return id;
    }
    
    // Fallback to the direct ID format (legacy)
    const id = path.startsWith('/') ? path.substring(1) : path;
    debugLog('Fallback extraction: ' + id);
    return id;
  }
  
  // Detect if user is on a mobile device
  function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }
  
  // Detect platform
  function getPlatform() {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    
    if (/android/i.test(userAgent)) return 'android';
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) return 'ios';
    if (/Windows/.test(userAgent)) return 'windows';
    if (/Mac/.test(userAgent)) return 'mac';
    
    return 'unknown';
  }
  
  // Track if page visibility changes (app opened)
  let appOpened = false;
  document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
      appOpened = true;
      debugLog('App appears to have opened - page hidden');
    }
  });
  
  // Try to open the app on Android with intent URL
  function tryOpenAndroidApp(shortId) {
    debugLog('Trying Android intent URL method');
    
    // Intent format has better compatibility with Chrome on Android
    const intentUrl = `intent://p/${shortId}#Intent;scheme=promz;package=ai.promz;end`;
    debugLog('Using intent URL: ' + intentUrl);
    window.location.href = intentUrl;
    
    // Also try the custom URL scheme as a fallback after a short delay
    setTimeout(function() {
      if (!appOpened) {
        const appUrl = `promz://p/${shortId}`;
        debugLog('Intent URL might have failed, trying custom scheme: ' + appUrl);
        window.location.href = appUrl;
      }
    }, 500);
  }
  
  // Handle deep link
  function handleDeepLink() {
    const shortId = getShortId();
    if (!shortId) {
      debugLog('No shortId found, nothing to do');
      return;
    }
    
    const platform = getPlatform();
    const isMobile = isMobileDevice();
    
    debugLog(`Handling deep link with ID: ${shortId}, platform: ${platform}, mobile: ${isMobile}`);
    
    // Try to open the app first
    if (isMobile) {
      if (platform === 'android') {
        // For Android, use the intent URL approach which has better compatibility
        tryOpenAndroidApp(shortId);
      } else {
        // For iOS, use the custom URL scheme
        debugLog('Trying iOS custom URL scheme');
        window.location.href = `promz://${shortId}`;
      }
      
      // Set a timeout to redirect to app store if app doesn't open
      setTimeout(function() {
        // Check if page became hidden (app opened)
        if (appOpened) {
          debugLog("App appears to have opened, not redirecting to store");
          return;
        }
        
        debugLog(`App not opened, redirecting to ${platform} store`);
        if (platform === 'ios') {
          window.location.href = APP_STORE_URL;
        } else if (platform === 'android') {
          window.location.href = PLAY_STORE_URL;
        } else {
          // Fallback to website
          redirectToDownloadPage(shortId);
        }
      }, 2500); // Increased timeout for better reliability (2.5 seconds)
    } else {
      // Desktop users - show download page
      debugLog('Desktop user, redirecting to download page');
      redirectToDownloadPage(shortId);
    }
  }
  
  // Redirect to download page with the ID as a parameter
  function redirectToDownloadPage(shortId) {
    const downloadUrl = `${WEBSITE_URL}/download.html?id=${shortId}`;
    debugLog('Redirecting to download page: ' + downloadUrl);
    window.location.href = downloadUrl;
  }
  
  // Run when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    debugLog('Page loaded, handling deep link');
    handleDeepLink();
  });
})();
